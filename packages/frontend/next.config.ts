import type { NextConfig } from "next";

const nextConfig: NextConfig = {
  webpack: (config, { isServer }) => {
    // Exclude canvas from server-side bundling to avoid SSR issues
    if (isServer) {
      config.externals = config.externals || [];
      config.externals.push('canvas');
    }
    return config;
  },
  // Disable static optimization for pages that use canvas
  experimental: {
    esmExternals: 'loose',
  },
};

export default nextConfig;
