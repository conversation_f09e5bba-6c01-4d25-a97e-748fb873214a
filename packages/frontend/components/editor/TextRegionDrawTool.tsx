'use client';

import { useState, useCallback } from 'react';
import { Rect } from 'react-konva';
import { useEditor } from './EditorContext';
import { TextRegionResponse, TextRegionType, TranslationStatus } from '@/types/api';

interface DrawingState {
  isDrawing: boolean;
  startX: number;
  startY: number;
  currentX: number;
  currentY: number;
}

export default function TextRegionDrawTool() {
  const { state, dispatch } = useEditor();
  const [drawingState, setDrawingState] = useState<DrawingState>({
    isDrawing: false,
    startX: 0,
    startY: 0,
    currentX: 0,
    currentY: 0,
  });

  // Handle mouse down to start drawing
  const handleMouseDown = useCallback((e: any) => {
    if (state.selectedTool !== 'text-region') return;

    const stage = e.target.getStage();
    const pointer = stage.getPointerPosition();

    // Convert screen coordinates to canvas coordinates
    const canvasX = (pointer.x - state.viewportOffset.x) / state.zoom;
    const canvasY = (pointer.y - state.viewportOffset.y) / state.zoom;

    setDrawingState({
      isDrawing: true,
      startX: canvasX,
      startY: canvasY,
      currentX: canvasX,
      currentY: canvasY,
    });
  }, [state.selectedTool, state.viewportOffset, state.zoom]);

  // Handle mouse move while drawing
  const handleMouseMove = useCallback((e: any) => {
    if (!drawingState.isDrawing || state.selectedTool !== 'text-region') return;

    const stage = e.target.getStage();
    const pointer = stage.getPointerPosition();

    // Convert screen coordinates to canvas coordinates
    const canvasX = (pointer.x - state.viewportOffset.x) / state.zoom;
    const canvasY = (pointer.y - state.viewportOffset.y) / state.zoom;

    setDrawingState(prev => ({
      ...prev,
      currentX: canvasX,
      currentY: canvasY,
    }));
  }, [drawingState.isDrawing, state.selectedTool, state.viewportOffset, state.zoom]);

  // Handle mouse up to finish drawing
  const handleMouseUp = useCallback(() => {
    if (!drawingState.isDrawing || state.selectedTool !== 'text-region') return;

    const width = Math.abs(drawingState.currentX - drawingState.startX);
    const height = Math.abs(drawingState.currentY - drawingState.startY);

    // Only create region if it's large enough
    if (width > 10 && height > 10) {
      const x = Math.min(drawingState.startX, drawingState.currentX);
      const y = Math.min(drawingState.startY, drawingState.currentY);

      // Create new text region
      const newRegion: TextRegionResponse = {
        id: `temp-${Date.now()}`, // Temporary ID
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        page_id: state.currentPage?.id || '',
        region_type: TextRegionType.SPEECH_BUBBLE,
        x,
        y,
        width,
        height,
        original_text: '',
        confidence_score: 1.0,
        translated_text: '',
        translation_status: TranslationStatus.PENDING,
        font_family: 'Arial',
        font_size: 14,
        font_color: '#000000',
        background_color: 'transparent',
      };

      dispatch({ type: 'ADD_TEXT_REGION', payload: newRegion });
      dispatch({ type: 'SET_SELECTED_REGIONS', payload: [newRegion.id] });
    }

    setDrawingState({
      isDrawing: false,
      startX: 0,
      startY: 0,
      currentX: 0,
      currentY: 0,
    });
  }, [drawingState, state.selectedTool, state.currentPage, dispatch]);

  // Calculate drawing rectangle dimensions
  const drawingRect = {
    x: Math.min(drawingState.startX, drawingState.currentX),
    y: Math.min(drawingState.startY, drawingState.currentY),
    width: Math.abs(drawingState.currentX - drawingState.startX),
    height: Math.abs(drawingState.currentY - drawingState.startY),
  };

  return (
    <>
      {/* Drawing preview rectangle */}
      {drawingState.isDrawing && drawingRect.width > 0 && drawingRect.height > 0 && (
        <Rect
          x={drawingRect.x}
          y={drawingRect.y}
          width={drawingRect.width}
          height={drawingRect.height}
          fill="rgba(59, 130, 246, 0.2)"
          stroke="#3b82f6"
          strokeWidth={2}
          dash={[5, 5]}
          listening={false}
        />
      )}
    </>
  );
}
