'use client';

import { useRef, useEffect, useCallback, useState } from 'react';
import { Stage, Layer, Image as KonvaImage, Rect, Text } from 'react-konva';
import { useEditor } from './EditorContext';
import { useImageLoader } from './hooks/useImageLoader';
import ImageUploader from './ImageUploader';
import TextRegion from './TextRegion';
import TextRegionDrawTool from './TextRegionDrawTool';
import TextEditOverlay from './TextEditOverlay';

export default function CanvasWorkspace() {
  const { state, dispatch } = useEditor();
  const stageRef = useRef<any>(null);
  const containerRef = useRef<HTMLDivElement>(null);

  // Sample manga page image (you can replace this with actual page data)
  const [sampleImageSrc, setSampleImageSrc] = useState<string | undefined>(undefined);
  const { image: mangaImage, isLoading: imageLoading, error: imageError } = useImageLoader(sampleImageSrc);

  // Text editing state
  const [editingRegion, setEditingRegion] = useState<{
    id: string;
    x: number;
    y: number;
    width: number;
    height: number;
    text: string;
  } | null>(null);

  // Load a sample image for demonstration
  const loadSampleImage = () => {
    // Using a placeholder image service for demonstration
    setSampleImageSrc('https://picsum.photos/400/600?random=1');
  };

  // Handle container resize
  useEffect(() => {
    const handleResize = () => {
      if (containerRef.current) {
        const { width, height } = containerRef.current.getBoundingClientRect();
        dispatch({
          type: 'SET_CANVAS_SIZE',
          payload: { width, height }
        });
      }
    };

    handleResize();
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, [dispatch]);

  // Handle wheel zoom with smooth scaling
  const handleWheel = useCallback((e: any) => {
    e.evt.preventDefault();

    const stage = stageRef.current;
    if (!stage) return;

    const oldScale = stage.scaleX();
    const pointer = stage.getPointerPosition();

    const scaleBy = 1.1;
    const newScale = e.evt.deltaY > 0 ? oldScale / scaleBy : oldScale * scaleBy;

    // Clamp zoom between 0.1x and 5x
    const clampedScale = Math.max(0.1, Math.min(5, newScale));

    dispatch({ type: 'SET_ZOOM', payload: clampedScale });

    // Adjust position to zoom towards mouse pointer
    const mousePointTo = {
      x: (pointer.x - stage.x()) / oldScale,
      y: (pointer.y - stage.y()) / oldScale,
    };

    const newPos = {
      x: pointer.x - mousePointTo.x * clampedScale,
      y: pointer.y - mousePointTo.y * clampedScale,
    };

    dispatch({ type: 'SET_VIEWPORT_OFFSET', payload: newPos });
  }, [dispatch]);

  // Handle stage drag (pan)
  const handleStageDrag = useCallback((e: any) => {
    dispatch({
      type: 'SET_VIEWPORT_OFFSET',
      payload: { x: e.target.x(), y: e.target.y() }
    });
  }, [dispatch]);

  // Handle stage click (deselect regions when clicking empty space)
  const handleStageClick = useCallback((e: any) => {
    // Check if clicked on empty area
    if (e.target === e.target.getStage()) {
      dispatch({ type: 'SET_SELECTED_REGIONS', payload: [] });
      // Close text editing if open
      setEditingRegion(null);
    }
  }, [dispatch]);

  // Check if Option/Alt key is pressed for panning
  const isPanningWithModifier = useCallback((e: any) => {
    return e.evt && (e.evt.altKey || e.evt.metaKey); // Alt on Windows/Linux, Option on Mac
  }, []);

  // Handle mouse down for potential panning
  const handleMouseDown = useCallback((e: any) => {
    if (isPanningWithModifier(e)) {
      // Enable dragging for panning with modifier
      const stage = e.target.getStage();
      stage.draggable(true);
    }
  }, [isPanningWithModifier]);

  // Handle mouse up to disable panning
  const handleMouseUp = useCallback((e: any) => {
    const stage = e.target.getStage();
    // Only allow dragging when pan tool is selected or modifier is pressed
    stage.draggable(state.selectedTool === 'pan');
  }, [state.selectedTool]);

  return (
    <div
      ref={containerRef}
      className="w-full h-full bg-gray-100 relative overflow-hidden"
    >
      {/* Canvas Grid Background */}
      {state.showGrid && (
        <div
          className="absolute inset-0 opacity-20"
          style={{
            backgroundImage: `
              linear-gradient(to right, #000 1px, transparent 1px),
              linear-gradient(to bottom, #000 1px, transparent 1px)
            `,
            backgroundSize: '20px 20px'
          }}
        />
      )}

      {/* Konva Stage */}
      <div data-canvas-container className="relative w-full h-full">
        <Stage
          ref={stageRef}
          width={state.canvasSize.width}
          height={state.canvasSize.height}
          scaleX={state.zoom}
          scaleY={state.zoom}
          x={state.viewportOffset.x}
          y={state.viewportOffset.y}
          onWheel={handleWheel}
          onDragEnd={handleStageDrag}
          onClick={handleStageClick}
          onMouseDown={handleMouseDown}
          onMouseUp={handleMouseUp}
          draggable={state.selectedTool === 'pan'}
        >
          <Layer>
            {/* Manga Page Background/Placeholder */}
            {!mangaImage && (
              <Rect
                x={50}
                y={50}
                width={400}
                height={600}
                fill="white"
                stroke="#e5e7eb"
                strokeWidth={1}
                shadowColor="black"
                shadowBlur={10}
                shadowOpacity={0.1}
                shadowOffsetX={2}
                shadowOffsetY={2}
              />
            )}

            {/* Manga Page Image */}
            {mangaImage && (
              <KonvaImage
                image={mangaImage}
                x={50}
                y={50}
                width={400}
                height={600}
                listening={false}
              />
            )}

            {/* Loading indicator */}
            {imageLoading && (
              <Text
                x={200}
                y={300}
                text="Loading image..."
                fontSize={16}
                fill="#6b7280"
                align="center"
                listening={false}
              />
            )}

            {/* Error indicator */}
            {imageError && (
              <Text
                x={200}
                y={300}
                text={`Error: ${imageError}`}
                fontSize={16}
                fill="#ef4444"
                align="center"
                listening={false}
              />
            )}

            {/* Text Region Draw Tool */}
            <TextRegionDrawTool />

            {/* Text Regions */}
            {state.textRegions.map((region) => (
              <TextRegion
                key={region.id}
                region={region}
                isSelected={state.selectedRegions.includes(region.id)}
                onSelect={() => {
                  if (state.selectedRegions.includes(region.id)) {
                    dispatch({
                      type: 'REMOVE_SELECTED_REGION',
                      payload: region.id
                    });
                  } else {
                    dispatch({
                      type: 'ADD_SELECTED_REGION',
                      payload: region.id
                    });
                  }
                }}
                onTransform={(updates) => {
                  dispatch({
                    type: 'UPDATE_TEXT_REGION',
                    payload: { id: region.id, updates }
                  });
                }}
                onStartEdit={(regionData) => {
                  setEditingRegion(regionData);
                }}
              />
            ))}
          </Layer>
        </Stage>

        {/* Canvas Overlay UI */}
        <div className="absolute top-4 right-4 space-y-2">
          {/* Zoom Info */}
          <div className="bg-white rounded-lg shadow-lg p-3">
            <div className="text-sm text-gray-600 font-medium">
              Zoom: {Math.round(state.zoom * 100)}%
            </div>
            <div className="text-xs text-gray-500 mt-1">
              Tool: {state.selectedTool}
            </div>
          </div>

          {/* Quick Actions */}
          <div className="bg-white rounded-lg shadow-lg p-2">
            <div className="flex flex-col space-y-1">
              <button
                onClick={() => dispatch({ type: 'SET_ZOOM', payload: 1 })}
                className="px-3 py-1 text-xs bg-gray-100 hover:bg-gray-200 rounded transition-colors"
              >
                Reset Zoom
              </button>
              <button
                onClick={() => dispatch({ type: 'SET_VIEWPORT_OFFSET', payload: { x: 0, y: 0 } })}
                className="px-3 py-1 text-xs bg-gray-100 hover:bg-gray-200 rounded transition-colors"
              >
                Center View
              </button>
              <button
                onClick={loadSampleImage}
                className="px-3 py-1 text-xs bg-blue-100 hover:bg-blue-200 text-blue-700 rounded transition-colors"
              >
                Load Sample
              </button>
              <ImageUploader onImageLoad={setSampleImageSrc} />
            </div>
          </div>
        </div>

        {/* Selection Info */}
        {state.selectedRegions.length > 0 && (
          <div className="absolute bottom-4 left-4 bg-blue-600 text-white rounded-lg shadow-lg p-3">
            <div className="text-sm font-medium">
              {state.selectedRegions.length} region{state.selectedRegions.length !== 1 ? 's' : ''} selected
            </div>
          </div>
        )}

        {/* Text Edit Overlay */}
        {editingRegion && (
          <TextEditOverlay
            isVisible={true}
            regionId={editingRegion.id}
            x={editingRegion.x}
            y={editingRegion.y}
            width={editingRegion.width}
            height={editingRegion.height}
            initialText={editingRegion.text}
            onComplete={(text) => {
              dispatch({
                type: 'UPDATE_TEXT_REGION',
                payload: {
                  id: editingRegion.id,
                  updates: { translated_text: text }
                }
              });
              setEditingRegion(null);
            }}
            onCancel={() => {
              setEditingRegion(null);
            }}
          />
        )}
      </div>
    </div>
  );
}
