'use client';

import { useEditor } from './EditorContext';

export default function StatusBar() {
  const { state } = useEditor();

  const getToolDisplayName = (tool: string) => {
    switch (tool) {
      case 'select': return 'Select Tool';
      case 'text-region': return 'Text Region Tool';
      case 'pan': return 'Pan Tool';
      case 'zoom': return 'Zoom Tool';
      default: return 'Unknown Tool';
    }
  };

  return (
    <div className="h-8 bg-gray-100 border-t border-gray-200 flex items-center justify-between px-4 text-xs text-gray-600">
      {/* Left Section - Current Tool */}
      <div className="flex items-center space-x-4">
        <span>
          <span className="font-medium">Tool:</span> {getToolDisplayName(state.selectedTool)}
        </span>

        {state.selectedRegions.length > 0 && (
          <span>
            <span className="font-medium">Selected:</span> {state.selectedRegions.length} region{state.selectedRegions.length !== 1 ? 's' : ''} • Double-click to edit text
          </span>
        )}

        <span className="text-gray-400">
          Option+drag to pan
        </span>
      </div>

      {/* Center Section - Page Info */}
      <div className="flex items-center space-x-4">
        {state.currentPage ? (
          <>
            <span>
              <span className="font-medium">Page:</span> {state.currentPage.page_number}
            </span>
            <span>
              <span className="font-medium">Size:</span> {state.currentPage.image_width}×{state.currentPage.image_height}
            </span>
          </>
        ) : (
          <span className="text-gray-400">No page loaded</span>
        )}
      </div>

      {/* Right Section - Canvas Info */}
      <div className="flex items-center space-x-4">
        <span>
          <span className="font-medium">Zoom:</span> {Math.round(state.zoom * 100)}%
        </span>
        <span>
          <span className="font-medium">Canvas:</span> {state.canvasSize.width}×{state.canvasSize.height}
        </span>
        {state.textRegions.length > 0 && (
          <span>
            <span className="font-medium">Regions:</span> {state.textRegions.length}
          </span>
        )}
      </div>
    </div>
  );
}
