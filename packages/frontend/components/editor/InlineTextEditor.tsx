'use client';

import { Html } from 'react-konva-utils';
import { useEffect, useRef } from 'react';
import Konva from 'konva';

// Fix text rendering issues
(Konva as any)._fixTextRendering = true;

interface InlineTextEditorProps {
  textNode: any; // Konva Text node
  onClose: () => void;
  onChange: (text: string) => void;
}

export default function InlineTextEditor({ textNode, onClose, onChange }: InlineTextEditorProps) {
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  useEffect(() => {
    if (!textareaRef.current || !textNode) return;

    const textarea = textareaRef.current;
    const stage = textNode.getStage();
    const stageContainer = stage.container();
    const stageRect = stageContainer.getBoundingClientRect();

    // Get the absolute position of the text node
    const absolutePosition = textNode.getAbsolutePosition();
    const scale = stage.scaleX(); // Canvas zoom level
    const stagePosition = stage.position(); // Canvas pan offset

    // Calculate the actual position on screen relative to the stage container
    const screenX = absolutePosition.x * scale + stagePosition.x + stageRect.left;
    const screenY = absolutePosition.y * scale + stagePosition.y + stageRect.top;

    // Match styles with the text node
    textarea.value = textNode.text();
    textarea.style.position = 'fixed'; // Use fixed positioning for better control
    textarea.style.top = `${screenY}px`;
    textarea.style.left = `${screenX}px`;
    textarea.style.width = `${textNode.width() * scale - 8}px`; // Account for padding
    textarea.style.height = `${textNode.height() * scale - 8}px`; // Account for padding
    textarea.style.fontSize = `${textNode.fontSize() * scale}px`;
    textarea.style.border = '2px solid #3b82f6';
    textarea.style.borderRadius = '4px';
    textarea.style.padding = '4px';
    textarea.style.margin = '0px';
    textarea.style.overflow = 'hidden';
    textarea.style.background = 'rgba(255, 255, 255, 0.98)';
    textarea.style.outline = 'none';
    textarea.style.resize = 'none';
    textarea.style.lineHeight = textNode.lineHeight() || '1.2';
    textarea.style.fontFamily = textNode.fontFamily() || 'Arial';
    textarea.style.transformOrigin = 'left top';
    textarea.style.textAlign = textNode.align() || 'center';
    textarea.style.color = textNode.fill() || '#000000';
    textarea.style.zIndex = '9999';
    textarea.style.boxShadow = '0 4px 12px rgba(0, 0, 0, 0.15)';
    textarea.style.minHeight = '1em';

    // Handle rotation if any
    const rotation = textNode.rotation();
    let transform = '';
    if (rotation) {
      transform += `rotateZ(${rotation}deg)`;
    }
    textarea.style.transform = transform;

    // Auto-resize height
    textarea.style.height = 'auto';
    textarea.style.height = `${textarea.scrollHeight + 8}px`;

    // Focus and select all text
    textarea.focus();
    textarea.select();

    const handleOutsideClick = (e: MouseEvent) => {
      if (e.target !== textarea) {
        onChange(textarea.value);
        onClose();
      }
    };

    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'Enter' && !e.shiftKey) {
        e.preventDefault();
        onChange(textarea.value);
        onClose();
      }
      if (e.key === 'Escape') {
        onClose();
      }
    };

    const handleInput = () => {
      // Auto-resize height based on content
      textarea.style.height = 'auto';
      textarea.style.height = `${textarea.scrollHeight + 8}px`;
    };

    // Add event listeners
    textarea.addEventListener('keydown', handleKeyDown);
    textarea.addEventListener('input', handleInput);

    // Delay adding click listener to prevent immediate close
    setTimeout(() => {
      window.addEventListener('click', handleOutsideClick);
    }, 100);

    return () => {
      textarea.removeEventListener('keydown', handleKeyDown);
      textarea.removeEventListener('input', handleInput);
      window.removeEventListener('click', handleOutsideClick);
    };
  }, [textNode, onChange, onClose]);

  return (
    <Html>
      <textarea
        ref={textareaRef}
        style={{
          minHeight: '1em',
          position: 'absolute',
        }}
        placeholder="Enter translation..."
      />
    </Html>
  );
}
